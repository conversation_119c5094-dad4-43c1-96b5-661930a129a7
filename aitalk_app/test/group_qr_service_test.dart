import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_demo_ios/core/models/user_qr_data.dart';
import 'package:flutter_demo_ios/core/services/group_qr_service.dart';

void main() {
  group('GroupQrData', () {
    test('should create group QR data correctly', () {
      final qrData = GroupQrData(
        groupId: 'ABC12345',
        groupName: '测试群组',
        password: 123456,
        channel: 5,
        members: {0: '0x12345678', 1: '0x87654321', 2: '0xABCDEF00'},
      );

      expect(qrData.groupId, 'ABC12345');
      expect(qrData.groupName, '测试群组');
      expect(qrData.password, 123456);
      expect(qrData.channel, 5);
      expect(qrData.memberCount, 3);
      expect(qrData.channelDisplayText('Channel'), 'Channel 5');
      expect(qrData.passwordDisplayText('No Password'), '123456');
    });

    test('should handle no password correctly', () {
      final qrData = GroupQrData(
        groupId: 'ABC12345',
        groupName: '无密码群组',
        password: 0,
        channel: 3,
        members: {0: '0x12345678'},
      );

      expect(qrData.passwordDisplayText('No Password'), 'No Password');
    });

    test('should convert to QR string correctly', () {
      final qrData = GroupQrData(
        groupId: 'ABC12345',
        groupName: '测试群组',
        password: 123456,
        channel: 5,
        members: {0: '0x12345678', 1: '0x87654321'},
      );

      final qrString = qrData.toQrString();
      expect(qrString, isNotEmpty);

      // 验证可以解码回原始数据
      final decodedData = GroupQrData.fromQrString(qrString);
      expect(decodedData, isNotNull);
      expect(decodedData!.groupId, qrData.groupId);
      expect(decodedData.groupName, qrData.groupName);
      expect(decodedData.password, qrData.password);
      expect(decodedData.channel, qrData.channel);
      expect(decodedData.members, qrData.members);
    });

    test('should return null for invalid QR string', () {
      // 测试无效的二维码字符串
      expect(GroupQrData.fromQrString('invalid'), isNull);
      expect(GroupQrData.fromQrString(''), isNull);

      // 测试非aiTalk二维码
      expect(GroupQrData.fromQrString('dGVzdA=='), isNull); // base64 of "test"
    });

    test('should return null for user QR code', () {
      // 创建一个用户二维码字符串
      const userQrData = UserQrData(
        frequency: 483600000,
        rateMode: 6,
        nickname: '测试用户',
        deviceId: '0x12345678',
      );

      final userQrString = userQrData.toQrString();

      // 群组二维码解析器应该返回null
      expect(GroupQrData.fromQrString(userQrString), isNull);
    });

    test('should validate group QR data correctly', () {
      // 有效数据
      final validData = GroupQrData(
        groupId: 'ABC12345',
        groupName: '测试群组',
        password: 123456,
        channel: 5,
        members: {0: '0x12345678'},
      );
      expect(GroupQrService.validateGroupQrData(validData), isTrue);

      // 无效群组ID
      final invalidGroupId = GroupQrData(
        groupId: '',
        groupName: '测试群组',
        password: 123456,
        channel: 5,
        members: {0: '0x12345678'},
      );
      expect(GroupQrService.validateGroupQrData(invalidGroupId), isFalse);

      // 无效群组名称
      final invalidGroupName = GroupQrData(
        groupId: 'ABC12345',
        groupName: '',
        password: 123456,
        channel: 5,
        members: {0: '0x12345678'},
      );
      expect(GroupQrService.validateGroupQrData(invalidGroupName), isFalse);

      // 无效信道
      final invalidChannel = GroupQrData(
        groupId: 'ABC12345',
        groupName: '测试群组',
        password: 123456,
        channel: 17, // 超出范围
        members: {0: '0x12345678'},
      );
      expect(GroupQrService.validateGroupQrData(invalidChannel), isFalse);

      // 负数密码
      final invalidPassword = GroupQrData(
        groupId: 'ABC12345',
        groupName: '测试群组',
        password: -1,
        channel: 5,
        members: {0: '0x12345678'},
      );
      expect(GroupQrService.validateGroupQrData(invalidPassword), isFalse);
    });

    test('should handle equality correctly', () {
      final qrData1 = GroupQrData(
        groupId: 'ABC12345',
        groupName: '测试群组',
        password: 123456,
        channel: 5,
        members: {0: '0x12345678', 1: '0x87654321'},
      );

      final qrData2 = GroupQrData(
        groupId: 'ABC12345',
        groupName: '测试群组',
        password: 123456,
        channel: 5,
        members: {0: '0x12345678', 1: '0x87654321'},
      );

      final qrData3 = GroupQrData(
        groupId: 'ABC12345',
        groupName: '不同群组',
        password: 123456,
        channel: 5,
        members: {0: '0x12345678', 1: '0x87654321'},
      );

      expect(qrData1, equals(qrData2));
      expect(qrData1, isNot(equals(qrData3)));
      expect(qrData1.hashCode, equals(qrData2.hashCode));
    });
  });
}
